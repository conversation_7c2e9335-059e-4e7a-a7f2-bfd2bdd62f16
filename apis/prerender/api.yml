openapi: "3.0.1"
info:
  title: "Pre-Render API Gateway HTTP API to EventBridge"
paths:
  /:
    post:
      responses:
        default:
          description: "Receive event data"
      x-amazon-apigateway-integration:
        integrationSubtype: "EventBridge-PutEvents"
        credentials:
          Fn::GetAtt: [PreRenderApiRole, Arn]
        requestParameters:
          Detail: "$request.body.Detail"
          DetailType: "$request.body.DetailType"
          Source: "$request.body.Source"
          EventBusName: dentalkart-eventbus-prod
        payloadFormatVersion: "1.0"
        type: "aws_proxy"
        connectionType: "INTERNET"
        timeoutInMillis: 3000
