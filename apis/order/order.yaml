AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Order API Gateway HTTP API to EventBridge

Resources:
  # Creates an HTTP API
  OrderApi:
    Type: AWS::Serverless::HttpApi
    Properties:
      DefinitionBody:
        "Fn::Transform":
          Name: "AWS::Include"
          Parameters:
            Location: "./api.yml"

  # Create the role for API Gateway access to EventBridge
  OrderApiRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service: "apigateway.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Policies:
        - PolicyName: ApiDirectWriteEventBridge
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              Action:
                - events:PutEvents
              Effect: Allow
              Resource:
                - !Sub arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/dentalkart-eventbus-prod

  # Create the role for API Gateway access to EventBridge
  CancelOrderApiRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service: "apigateway.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Policies:
        - PolicyName: ApiDirectWriteEventBridge
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              Action:
                - events:PutEvents
              Effect: Allow
              Resource:
                - !Sub arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/dentalkart-eventbus-prod
