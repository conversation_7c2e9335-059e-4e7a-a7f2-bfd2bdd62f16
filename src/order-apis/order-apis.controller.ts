import {
  Post,
  Controller,
  ValidationPipe,
  Req,
  Body,
  Get,
  Param,
} from '@nestjs/common';

import { CreateOrderDto } from '../dto/create-order.dto';
import { FetchOrderDto } from '../dto/fetch-payment.dto';
import { CodOrderPayload } from '../dto/cod-order-status.dto';
import { OrderApisService } from './order-apis.service';
import { Request } from 'express';
import {
  FireOrderEventDto,
  FireOrderEventResponseDto,
} from 'src/dto/order-event.dto';
import { OrderEventService } from './order-event.service';

@Controller('order/api/v1/orders')
export class OrderApisController {
  constructor(
    private readonly orderApisService: OrderApisService,
    private readonly orderEventService: OrderEventService,
  ) {}

  @Post()
  async createOrder(
    @Body(new ValidationPipe({ transform: true }))
    createOrderDto: CreateOrderDto,
    @Req() req: Request,
  ) {
    return this.orderApisService.createOrder(createOrderDto, req);
  }

  @Post('/payments')
  async fetchOrderPaymentsDetails(
    @Body(new ValidationPipe({ transform: true }))
    fetchOrderDto: FetchOrderDto,
    @Req() req: Request,
  ) {
    return this.orderApisService.fetchOrderPaymentsDetails(fetchOrderDto, req);
  }

  @Post('cod-order')
  async placeCodOrder(
    @Body(new ValidationPipe({ transform: true }))
    updateOrderDto: CodOrderPayload,
    @Req() req: Request,
  ) {
    return this.orderApisService.placeCodOrder(updateOrderDto, req);
  }

  @Post('events-occurred')
  async fireOrderEvent(
    @Body() fireOrderEventDto: FireOrderEventDto,
    @Req() req: Request,
  ): Promise<FireOrderEventResponseDto> {
    const { order_id, action } = fireOrderEventDto;
    try {
      const eventFired = await this.orderEventService.fireOrderEvent(
        order_id,
        action,
        req,
      );

      return {
        message: eventFired
          ? `Event "${action}" for order ${order_id} has been fired successfully.`
          : `Event "${action}" for order ${order_id} has already been fired.`,
        event_fired: eventFired,
      };
    } catch (error) {
      return {
        message: `Event "${action}" for order ${order_id} has been failed.`,
        event_fired: false,
      };
    }
  }

  @Get('/payment/razorpay/methods')
  getPaymentMethods() {
    return this.orderApisService.getRazorpayMethods();
  }

  @Get(':order_id/details')
  async getOrderDetails(
    @Param('order_id') order_id: string,
    @Req() req: Request,
  ) {
    return this.orderApisService.getOrderDetails(order_id, req);
  }
}
