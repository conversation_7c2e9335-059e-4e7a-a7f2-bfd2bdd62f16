AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Kapture Lambda functions to add/update customers in kapture

####################################################################################
Parameters:
  ####################################################################################

  LambdaRate:
    Description: >
      The rate (frequency) that determines when CloudWatch Events runs the rule that
      triggers the Lambda function.
    Default: rate(1 day)
    AllowedValues:
      - rate(1 day)
      - rate(1 minute)
    Type: String

Resources:
  #**********************************************************************************#
  # Lambda for syncing customer in kapture
  #**********************************************************************************#

  # Creates lambda for add/update customer in kapture
  KaptureUpdateCustomerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref AxiosLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  LambdaExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  LambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref LambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${KaptureUpdateCustomerFunction.Arn}
          Id: LambdaSchedule

  #Create a Permission
  LambdaSchedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !Sub ${KaptureUpdateCustomerFunction.Arn}
      Principal: "events.amazonaws.com"
      SourceArn: !Sub ${LambdaSchedule.Arn}

  #Create axios layer
  AxiosLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: axios
      Description: Dependencies for Kapture
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
