AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: cart Lambda functions to sync data

Resources:
  # Creates lambda for syncing order data
  CartFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  CartEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "CartEventRule"
      Name: "CartEventRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.cart.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt CartFunction.Arn
          Id: CartLmdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeCartLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: CartFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "CartEventRule"
          - "Arn"
