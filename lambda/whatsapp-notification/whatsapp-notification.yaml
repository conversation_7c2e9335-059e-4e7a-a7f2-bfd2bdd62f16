AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Whatsapp notification lambda to send messages

Resources:
  # creating the lambda
  WhatsappNotificationHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref WhatsappNotificationDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  WhatsappNotificationEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling function"
      Name: "WhatsappNotificationEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.whatsapp.notification

      State: ENABLED
      Targets:
        - Arn: !GetAtt WhatsappNotificationHandlerFunction.Arn
          Id: WhatsappNotificationLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeWhatsappNotificationLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: WhatsappNotificationHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "WhatsappNotificationEventRule"
          - "Arn"

  #Create axios layer
  WhatsappNotificationDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: whatsappNotificationDependencies
      Description: Dependencies for whatsapp-notification
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
