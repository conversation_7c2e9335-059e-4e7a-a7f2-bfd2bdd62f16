/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    const { detail: data } = event;
    let sample_data = JSON.stringify({
        "distinct_id": "+************",
        "phone": "+************",
        "event": "notify_on_return_create",
        "data": {
          "order_id": "100001",
          "return_ID": "100001-1",
          "Name": "Manish",
          "Date": "27th Nov 2024 18:00:00"
        }
    }); 
    console.log(`Whatsapp notification request: ${JSON.stringify(data)}`)
    const res = await axios.post(API.url, sample_data, {
      headers: {
        "Content-Type": "application/json",
        "x-limechat-uat": "lcuat.YwkSdbIyk@@F8qUu&Y&x_lUhymR56!^8G+65DFH3mw+h@IVg*uUiQWr&ylueWgl+Er9h%U-Sl#PPUS9z*ZC1QV*05&C6l5B0mo%enr5$HQ0n%duS",
        "x-fb-account-id": "11236",
      }
    });
    console.log(`Whatsapp notification response: ${res}`)
  } catch (e) {
    console.log("Error in whatsapp handler", e);
  }
};
