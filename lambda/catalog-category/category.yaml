AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Products lambda for saving products in elasticsearch

Resources:
  # creating the lambda
  CategoryRequestHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref CategoryDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  CategoryEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling CategoryRequestHandlerFunction"
      Name: "CategoryEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - prod.dentalkart.catalog.category.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt CategoryRequestHandlerFunction.Arn
          Id: CategoryLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeCategoryLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: CategoryRequestHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "CategoryEventRule"
          - "Arn"

  #Create axios layer
  CategoryDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: categoryDependencies
      Description: Dependencies for category
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
