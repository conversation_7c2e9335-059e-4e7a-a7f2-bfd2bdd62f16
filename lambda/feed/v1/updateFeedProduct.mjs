/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    console.log(`product_feed_update_event: ${JSON.stringify(event)}`)

    const { detail: webook_data } = event;
    if (webook_data) {
      const res = await axios.post(API.feed_api_url, webook_data, {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": API.feed_api_key
        },
      });
      return {
        statusCode: 200,
        body: JSON.stringify({
          data: res?.data,
        }),
      };
    } else {
      return {
        statusCode: 200,
        body: JSON.stringify({
          isError: false,
          data: null,
          message: "Webhook data of undefined",
        }),
      };
    }
  } catch (e) {
    console.log("error_feed_webhook_payload", event?.detail);
    console.log("Error in updating the product feed webhook", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
