AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Products Lambda functions to receive product data


Resources:
  #**********************************************************************************#
  # Lambda for receiving data
  #**********************************************************************************#

  # Creates lambda for receiving product data
  UpdateProductFeedFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: updateFeedProduct.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  UpdateProductFeedEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "UpdateProductFeedEventRule"
      Name: "UpdateProductFeedEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.catalog.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt UpdateProductFeedFunction.Arn
          Id: UpdateProductFeedLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeUpdateProductFeedLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: UpdateProductFeedFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "UpdateProductFeedEventRule"
          - "Arn"

  #Create axios layer
  ProductDependeciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
        LayerName: productDependecies
        Description: Dependencies for product
        ContentUri: dependencies/
        CompatibleRuntimes:
          - nodejs6.10
          - nodejs8.10
          - nodejs18.x
        LicenseInfo: 'MIT'
        RetentionPolicy: Retain
