AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Products lambda for saving url resolvers in mongo

Resources:
  # creating the lambda
  UrlResolverRequestHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref UrlResolverDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  UrlResolverEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling UrlResolverRequestHandlerFunction"
      Name: "UrlResolverEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - prod.dentalkart.catalog.urlresolver.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt UrlResolverRequestHandlerFunction.Arn
          Id: UrlResolverLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeUrlResolverLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: UrlResolverRequestHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "UrlResolverEventRule"
          - "Arn"

  #Create axios layer
  UrlResolverDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: categoryDependencies
      Description: Dependencies for url resolver
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
