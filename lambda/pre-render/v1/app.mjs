import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    const { detail } = event;
    const eventType = detail["event-type"];

    if (eventType === "add-entry") {
      const res = await axios.post(
        `${API.cache_manager_url}/cache-manager/add`,
        {
          url: detail.url,
        },
        {
          headers: {
            "x-api-key": API.cache_manager_api_key,
          },
        }
      );

      return {
        statusCode: 200,
        body: JSON.stringify({
          data: res?.data,
        }),
      };
    } else if (eventType === "update-entry") {
      const res = await axios.post(
        `${API.cache_manager_url}/cache-manager/update`,
        {
          pageId: detail.pageId,
        },
        {
          headers: {
            "x-api-key": API.cache_manager_api_key,
          },
        }
      );

      return {
        statusCode: 200,
        body: JSON.stringify({
          data: res?.data,
        }),
      };
    } else {
      throw new Error("invalid event-type");
    }
  } catch (e) {
    console.log("error_notify_webhook_payload", event?.detail);
    console.log("error_in_notify_webhook", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
