import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  const { detail } = event;

  const { page_id, start_time, url, device_type, origin, originating_crawler } =
    detail;

  const res = await axios.post(
    `${API.queue_manager_url}/render`,
    { url, device_type, page_id, start_time, origin, originating_crawler },
    {
      headers: {
        "x-api-key": API.queue_manager_api_key,
      },
    }
  );

  return {
    statusCode: 200,
    body: JSON.stringify({
      data: res?.data,
    }),
  };
};
