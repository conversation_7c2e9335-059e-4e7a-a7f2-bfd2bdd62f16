AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Pre render lambda to trigger rendering

Resources:
  # creating the lambda
  PreRenderRequestHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref PreRenderDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  PreRenderRendererFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: renderer.lambdaHandler
      Layers:
        - !Ref PreRenderDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  PreRenderEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling cache-manager"
      Name: "PreRenderEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.prerender.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt PreRenderRequestHandlerFunction.Arn
          Id: PreRenderLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Role
  PreRenderRendererEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling cache-manager"
      Name: "PreRenderRendererEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.prerender.renders.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt PreRenderRendererFunction.Arn
          Id: PreRenderRendererTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeCartLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: PreRenderRequestHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "PreRenderEventRule"
          - "Arn"

            #Create a Permission
  PermissionForEventsToInvokeRendererLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: PreRenderRendererFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "PreRenderRendererEventRule"
          - "Arn"

  #Create axios layer
  PreRenderDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: preRenderDependencies
      Description: Dependencies for pre-render
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
