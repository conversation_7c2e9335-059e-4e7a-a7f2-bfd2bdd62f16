/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  console.log(JSON.stringify(event));
  try {
    const { detail } = event;
    let {
      customer_id,
      entity_value,
      event_type,
      source,
      status,
      template_id,
      subject,
      message,
      from,
      to,
      body,
      event_id,
      bcc_email_id,
      ...rest
    } = detail;

    const eventDetailsType = detail["detail-type"];
    let url = "";
    if (eventDetailsType == "email-notifications") {
      url = `${API.notification_url}/email`;
    } else if (eventDetailsType == "sms-notifications") {
      url = `${API.notification_url}/sms`;
    } else if (eventDetailsType == "whatsapp-notifications") {
      url = `${API.notification_url}/whatsapp`;
    } else {
      throw new Error("invalid event-type");
    }

    const headers = {
      "Content-Type": "application/json",
      "x-api-key": API.notification_key,
    };

    const data = {
      customer_id,
      entity_value,
      event_type,
      source,
      status,
      template_id,
      subject,
      message,
      from,
      to,
      body,
      event_id,
      bcc_email_id,
      ...rest,
    };

    const response = await axios.post(url, data, { headers });

    console.log("Response:", response.data);

    return {
      statusCode: response.status,
      body: JSON.stringify(response.data),
    };
  } catch (e) {
    console.log("Error in notify function", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
