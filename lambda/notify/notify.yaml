AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Notify lambda for sending notifications

Resources:
  # creating the lambda
  NotifyRequestHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref NotifyDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  NotifyEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling NotifyRequestHandlerFunction"
      Name: "NotifyEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.notifications.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt NotifyRequestHandlerFunction.Arn
          Id: PreRenderLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeNotifyLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: NotifyRequestHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "NotifyEventRule"
          - "Arn"

  #Create axios layer
  NotifyDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: notifyDependencies
      Description: Dependencies for pre-render
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
