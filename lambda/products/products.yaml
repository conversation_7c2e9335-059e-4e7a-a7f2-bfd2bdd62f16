AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Products Lambda functions to receive product data

Parameters:
####################################################################################

  LambdaRate:
    Description: >
      The rate (frequency) that determines when CloudWatch Events runs the rule that
      triggers the Lambda function.
    Default: rate(2 hours)
    AllowedValues:
      - rate(1 hour)
      - rate(2 hours)
      - rate(3 hours)
      - rate(4 hours)
    Type: String


Resources:
  #**********************************************************************************#
  # Lambda for receiving data
  #**********************************************************************************#

  # Creates lambda for receiving product data
  ReviewsGetDataFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: reviews.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  ReviewsEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "ReviewsEventRule"
      Name: "ReviewsEventBusRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.product.reviews.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt ReviewsGetDataFunction.Arn
          Id: ReviewsLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: ReviewsGetDataFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "ReviewsEventRule"
          - "Arn"

  # Creates lambda for processing review status events
  ReviewsStatusFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: reviewsStatus.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  ReviewsStatusEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "ReviewsStatusEventRule"
      Name: "ReviewsStatusEventBusRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.product.reviews.status

      State: ENABLED
      Targets:
        - Arn: !GetAtt ReviewsStatusFunction.Arn
          Id: ReviewsStatusLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeReviewsStatusLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: ReviewsStatusFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "ReviewsStatusEventRule"
          - "Arn"


  # # Creates lambda for updating group product stock

  # UpdateGroupProductFunction:
  #   Type: AWS::Serverless::Function
  #   Properties:
  #     CodeUri: ./v1
  #     Handler: updateGroupProductStock.lambdaHandler
  #     Layers:
  #       - !Ref ProductDependeciesLayer
  #     Runtime: nodejs18.x
  #     MemorySize: 128
  #     Timeout: 120
  #     Architectures:
  #       - x86_64

  #   #Create a Role
  # UpdateProductEventRule:
  #   Type: "AWS::IAM::Role"
  #   Properties:
  #     AssumeRolePolicyDocument:
  #       Version: 2012-10-17
  #       Statement:
  #         - Effect: Allow
  #           Principal:
  #             Service:
  #               - lambda.amazonaws.com
  #           Action:
  #             - sts:AssumeRole
  #     ManagedPolicyArns:
  #       - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  # #Create a Scheduler
  # LambdaSchedule:
  #   Type: "AWS::Events::Rule"
  #   Properties:
  #     Description: >
  #       A schedule for the Lambda function..
  #     ScheduleExpression: !Ref LambdaRate
  #     State: ENABLED
  #     Targets:
  #       - Arn: !Sub ${UpdateGroupProductFunction.Arn}
  #         Id: LambdaSchedule

  # #Create a Permission
  # LambdaSchedulePermission:
  #   Type: "AWS::Lambda::Permission"
  #   Properties:
  #     Action: 'lambda:InvokeFunction'
  #     FunctionName: !Sub ${UpdateGroupProductFunction.Arn}
  #     Principal: 'events.amazonaws.com'
  #     SourceArn: !Sub ${LambdaSchedule.Arn}


  # Creates lambda for receiving product data
  UpdateProductFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: updateProduct.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  UpdateProductEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "UpdateProductEventRule"
      Name: "UpdateProductEventBusRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.catalog.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt UpdateProductFunction.Arn
          Id: UpdateProductLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeUpdateProductLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: UpdateProductFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "UpdateProductEventRule"
          - "Arn"

  # Creates lambda for updating product tier price
  UpdateProductTierPriceFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: updateTierPrice.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  UpdateProductTierPriceEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "UpdateProductTierPriceEventRule"
      Name: "UpdateProductTierPriceEventBusRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.catalog.updateTierPrice

      State: ENABLED
      Targets:
        - Arn: !GetAtt UpdateProductTierPriceFunction.Arn
          Id: UpdateProductTierPriceLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeUpdateProductTierPriceLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: UpdateProductTierPriceFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "UpdateProductTierPriceEventRule"
          - "Arn"

  # Creates lambda for receiving product attributes data
  CatalogProductAttributesGetDataFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: catalogProductAttributes.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  CatalogProductAttributesEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "CatalogProductAttributesEventRule"
      Name: "CatalogProductAttributesEventBusRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.product.reviews.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt CatalogProductAttributesGetDataFunction.Arn
          Id: CatalogProductAttributesLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeCatalogProductAttributesLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: CatalogProductAttributesGetDataFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "CatalogProductAttributesEventRule"
          - "Arn"
  
  # Creates lambda for  product attachment
  UpdateProductAttachmentFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: updateProductAttachment.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  UpdateProductAttachmentEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "UpdateProductAttachmentEventRule"
      Name: "UpdateProductAttachmentEventBusRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.catalog.updateProductAttachment

      State: ENABLED
      Targets:
        - Arn: !GetAtt UpdateProductAttachmentFunction.Arn
          Id: UpdateProductAttachmentLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeUpdateProductAttachmentLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: UpdateProductAttachmentFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "UpdateProductAttachmentEventRule"
          - "Arn"

  # Creates lambda for  product tags
  UpdateProductTagsFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: updateProductTags.lambdaHandler
      Layers:
        - !Ref ProductDependeciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  UpdateProductTagsEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "UpdateProductTagsEventRule"
      Name: "UpdateProductTagsEventBusRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.catalog.updateProductTags

      State: ENABLED
      Targets:
        - Arn: !GetAtt UpdateProductTagsFunction.Arn
          Id: UpdateProductTagsLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeUpdateProductTagsLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: UpdateProductTagsFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "UpdateProductTagsEventRule"
          - "Arn"

  #Create axios layer
  ProductDependeciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: productDependecies
      Description: Dependencies for product
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
