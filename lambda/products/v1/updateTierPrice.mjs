/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    console.log(`product_update_tier_price_event: ${JSON.stringify(event)}`)
    const { detail: tier_price_sku } = event;
    if (tier_price_sku) {
      const res = await axios.post(API.update_product_tier_price, tier_price_sku, {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": API.update_product_tier_price_key
        },
      });
      return {
        statusCode: 200,
        body: JSON.stringify({
          data: res?.data,
        }),
      };
    } else {
      return {
        statusCode: 200,
        body: JSON.stringify({
          isError: false,
          data: null,
          message: "Product sku of undefined",
        }),
      };
    }
  } catch (e) {
    console.log("error_webhook_payload", event?.detail);
    console.log("Error in updating the product tier price", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
