/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    console.log(`group_product_stock_update_event: ${JSON.stringify(event)}`)
      const res = await axios.get(API.update_group_product_stock, {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": API.update_product_tier_price_key
        },
      });
      console.log("response from update group api", res.data);
      if(res.data){
        return {
            statusCode: 200,
            body: "Stock updated"
          };
      }
    } catch (e) {
    console.log("error in updating group product stock ");
    console.log("Error in updating the product", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
