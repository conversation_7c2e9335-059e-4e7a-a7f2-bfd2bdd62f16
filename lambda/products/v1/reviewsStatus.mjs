import axios from "axios";

// Configuration can be loaded from a config file if needed
const API_URL =
  "https://order-tracking.dentalkart.com/order-tracking/api/v1/admin/orders/update-review-status";

/**
 * Lambda handler to process product reviews status events
 * @param {Object} event - The event object from EventBridge
 * @param {Object} context - The Lambda context
 * @returns {Object} - Response object
 */
export const lambdaHandler = async (event, context) => {
  console.log("Event received:", JSON.stringify(event));

  try {
    // Extract the event detail (the actual payload)
    const payload = event.detail || event;

    // Log the received payload for debugging
    console.log("Processing payload:", JSON.stringify(payload));

    // Forward the payload to the target API
    const response = await axios.post(API_URL, payload, {
      headers: {
        "Content-Type": "application/json",
        "x-api-key": "ba0d1b61c8df4e399a554ba04023b78315ea352911d24edcbc4141b",
      },
    });

    console.log(
      "API response:",
      response.status,
      JSON.stringify(response.data)
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Event processed successfully",
        eventId: event.id || "unknown",
        apiResponse: response.status,
      }),
    };
  } catch (err) {
    console.error("Error processing event:", err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error processing event",
        error: err.message,
      }),
    };
  }
};
