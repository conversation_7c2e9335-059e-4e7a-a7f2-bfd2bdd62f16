/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    console.log(`review_event: ${JSON.stringify(event)}`)
    const { detail: webhook_data } = event;
    if (webhook_data) {
        const {product_id, sku, avg_rating, review_count} = webhook_data;

        let body = JSON.stringify({
            "product_id": product_id,
            "custom_attributes": [
                {
                    "attribute_code": "rw_svc_review_count",
                    "value": review_count
                },
                {
                    "attribute_code": "rw_svc_avg_rating",
                    "value": avg_rating
                }
            ]
        });

        let url = API.post_review_attr_api_url;
        let x_api_key = API.post_review_attr_api_key;

        const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: url,
            headers: {
                'x-api-key': x_api_key,
                'Content-Type': 'application/json'
            },
            data : body
        };

        const res = await axios(config);
        const result = res.data;
        const response = {
            statusCode: 200,
            body: JSON.stringify(result),
        };
        console.log(url, JSON.stringify(result))
        return response;
    } else {
        return {
            statusCode: 200,
            body: JSON.stringify({
            isError: false,
            data: null,
            message: "Webhook data of undefined",
            }),
        };
    }
  } catch (e) {
    console.log("error_webhook_payload", event?.detail);
    return {
        statusCode: 500,
        body: JSON.stringify({
            isError: true,
            data: null,
            message: e?.message,
        }),
    };
  }
};
