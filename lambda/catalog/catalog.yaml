AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Products lambda for saving products in elasticsearch

Resources:
  # creating the lambda
  ProductRequestHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref ProductDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  ProductEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling ProductRequestHandlerFunction"
      Name: "ProductEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - test.dentalkart.catalog.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt ProductRequestHandlerFunction.Arn
          Id: ProductLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeProductLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: ProductRequestHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "ProductEventRule"
          - "Arn"

  #Create axios layer
  ProductDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: productDependencies
      Description: Dependencies for product
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
