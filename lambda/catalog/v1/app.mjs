/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  console.log("Product mongo test sync", JSON.stringify(event));
  try {
    const { detail } = event;

    // const eventDetailsType = detail["detail-type"];
    let url = `${API.catalog_url}/product`;

    const headers = {
      "Content-Type": "application/json",
      "x-api-key": API.catalog_key,
    };

    const response = await axios.post(url, detail, { headers });

    console.log("Response:", response.data);

    return {
      statusCode: response.status,
      body: JSON.stringify(response.data),
    };
  } catch (e) {
    console.log("Error in catalog eventbus function", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
