AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Products lambda for saving products in elasticsearch

####################################################################################
Parameters:
  ####################################################################################
  LambdaRate:
    Description: >
      The rate (frequency) that determines when CloudWatch Events runs the rule that
      triggers the Lambda function.
    Default: rate(5 minutes)
    AllowedValues:
      - rate(1 minute)
      - rate(5 minutes)
      - rate(10 minutes)
    Type: String

Resources:
  # creating the lambda
  CatalogRequestHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref CatalogDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  CatalogEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling CatalogRequestHandlerFunction"
      Name: "CatalogEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - prod.dentalkart.catalog.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt CatalogRequestHandlerFunction.Arn
          Id: CatalogLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeCatalogLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: CatalogRequestHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "CatalogEventRule"
          - "Arn"

  # ProductSyncFetchFunction:
  #   Type: AWS::Serverless::Function
  #   Properties:
  #     CodeUri: ./v1
  #     Handler: productSyncFetch.lambdaHandler
  #     Layers:
  #       - !Ref CatalogDependenciesLayer
  #     Runtime: nodejs18.x
  #     MemorySize: 128
  #     Timeout: 15
  #     Architectures:
  #       - x86_64
  #     Role: !GetAtt ProductSyncFetchRole.Arn # Referencing the IAM Role (fix below)

  #   # Create a Role with the necessary permissions for Lambda execution
  # ProductSyncFetchRole:
  #   Type: AWS::IAM::Role
  #   Properties:
  #     AssumeRolePolicyDocument:
  #       Version: 2012-10-17
  #       Statement:
  #         - Effect: Allow
  #           Principal:
  #             Service:
  #               - lambda.amazonaws.com
  #           Action:
  #             - sts:AssumeRole
  #     ManagedPolicyArns:
  #       - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #     # Create a Scheduler rule that triggers the Lambda every 5 minutes
  # ProductSchedule:
  #   Type: AWS::Events::Rule
  #   Properties:
  #     Description: "Schedule rule to trigger ProductSyncFetchFunction every 5 minutes"
  #     ScheduleExpression: "rate(5 minutes)" # Lambda triggered every 5 minutes
  #     State: ENABLED
  #     Targets:
  #       - Arn: !GetAtt ProductSyncFetchFunction.Arn
  #         Id: ProductScheduleLambdaTarget
  #         RoleArn: !GetAtt ProductSyncFetchRole.Arn # The role the scheduler will use to invoke the Lambda

  # # Create a Permission for EventBridge to invoke the Lambda function
  # ProductSchedulePermission:
  #   Type: AWS::Lambda::Permission
  #   Properties:
  #     Action: "lambda:InvokeFunction"
  #     FunctionName: !Ref ProductSyncFetchFunction
  #     Principal: "events.amazonaws.com"
  #     SourceArn: !GetAtt ProductSchedule.Arn # Attach permission for EventBridge rule

  # Creates lambda for product update++++++
  ProductSyncCronHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: productSyncCron.lambdaHandler
      Layers:
        - !Ref CatalogDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 25
      Architectures:
        - x86_64

  #Create a Role
  ProductSyncCronExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  ProductSyncCronLambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref LambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${ProductSyncCronHandler.Arn}
          Id: ProductSyncCron

  #Create a Permission
  ProductSyncCronSchedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !Sub ${ProductSyncCronHandler.Arn}
      Principal: "events.amazonaws.com"
      SourceArn: !Sub ${ProductSyncCronLambdaSchedule.Arn}

  # Creates lambda for category sync++++++
  CategorySyncCronHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: categorySyncCron.lambdaHandler
      Layers:
        - !Ref CatalogDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 25
      Architectures:
        - x86_64

  #Create a Role
  CategorySyncCronExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  CategorySyncCronLambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref LambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${CategorySyncCronHandler.Arn}
          Id: CategorySyncCron

  #Create a Permission
  CategorySyncCronSchedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !Sub ${CategorySyncCronHandler.Arn}
      Principal: "events.amazonaws.com"
      SourceArn: !Sub ${CategorySyncCronLambdaSchedule.Arn}

  # Creates lambda for urlRewrite  updates++++++
  UrlRewriteSyncCronHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: urlRewriteSyncCron.lambdaHandler
      Layers:
        - !Ref CatalogDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 25
      Architectures:
        - x86_64

  #Create a Role
  UrlRewriteSyncCronExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  UrlRewriteSyncCronLambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref LambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${UrlRewriteSyncCronHandler.Arn}
          Id: UrlRewriteSyncCron

  #Create a Permission
  UrlRewriteSyncCronSchedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !Sub ${UrlRewriteSyncCronHandler.Arn}
      Principal: "events.amazonaws.com"
      SourceArn: !Sub ${UrlRewriteSyncCronLambdaSchedule.Arn}

  # creating the lambda
  VinculumSyncHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: vinculumSkuCreateUpdate.createOrUpdateSkuHandler
      Layers:
        - !Ref CatalogDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  VinculumSyncEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling VinculumSyncHandlerFunction"
      Name: "VinculumSyncEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - prod.dentalkart.catalog.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt VinculumSyncHandlerFunction.Arn
          Id: VinculumSyncLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeVinculumSyncLamda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: VinculumSyncHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "VinculumSyncEventRule"
          - "Arn"

  #Create axios layer
  CatalogDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: catalogDependencies
      Description: Dependencies for product
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
