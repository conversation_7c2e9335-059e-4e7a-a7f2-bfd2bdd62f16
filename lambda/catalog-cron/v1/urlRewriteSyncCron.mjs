/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    console.log(
      "**********sync Update UrlReWrite stared********",
      JSON.stringify(event)
    );
    const res = await axios.get(API.url_reWrite_update_url, {
      headers: {
        "Content-Type": "application/json",
        "x-api-key": API.catalog_update_api_key,
      },
    });
    console.log(`res ${res}`);
    console.log("**********sync Update UrlReWrite End********");
    return {
      statusCode: 200,
      body: JSON.stringify({
        data: "success",
      }),
    };
  } catch (e) {
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: e?.message || e,
        error: e?.message || e,
      }),
    };
  }
};
