const CATALOG_BASE_URL = `https://catalog-service.dentalkart.com`;
const CATALOG_API_KEY = `dentalkart101`;

export const API = {
  catalog_url:
    "https://serverless-prod.dentalkart.com/api/v1/catalogue/webhook",
  catalog_key: "ZFobrRyccnTyXyXHPUVO4eyyKEKoSjWB",
  catalog_scheduler_fetch_api_url:
    "https://catalog-service.dentalkart.com/v1/catalog-admin/elastic-search-crons",
  catalog_service_api_key: "dentalkart101",
  product_update_url: `${CATALOG_BASE_URL}/v1/catalog-admin/sync/products`,
  category_update_url: `${CATALOG_BASE_URL}/v1/catalog-admin/sync/category`,
  url_reWrite_update_url: `${CATALOG_BASE_URL}/v1/catalog-admin/sync/url-rewrite`,
  catalog_update_api_key: CATALOG_API_KEY,
  vinculum: {
    create_sku_url:
      "https://dentalkart.vineretail.com/RestWS/api/eretail/v1/sku/create",
    update_sku_url:
      "https://dentalkart.vineretail.com/RestWS/api/eretail/v1/sku/update",
    //wrong kry passed
    api_key: "29b15a8e9c0f4091934b4a025fed3d9f26bf13412b664e6693a1ca7-test",
    api_owner: "Manish",
  },
};
