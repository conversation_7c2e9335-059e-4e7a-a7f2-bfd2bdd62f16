import axios from "axios";
import { API } from "./config/api.mjs";

/**
 * Function to map SKU data to the update format
 * @param {Object} skuData - The SKU data to be updated
 * @returns {Object} - The updated format for SKU
 */
const mapUpdateSkuData = (detail) => {
  const slicedShortName = detail?.name?.slice(0, 10);
  const taxCategory = detail?.custom_attributes?.find(
    (data) => data.attribute_code === "hsn_code"
  );
  const salePrice = detail?.custom_attributes?.find(
    (data) => data.attribute_code === "special_price"
  );

  return {
    request: [
      {
        skuCode: detail.sku,
        skuName: detail?.name ?? "",
        skuShortName: slicedShortName,
        mrp: detail.price,
        salePrice: salePrice?.value || "",
        taxCategory: taxCategory?.value ?? "",
      },
    ],
  };
};

/**
 * Map the SKU data into the format needed for the API
 * @param {Object} detail - The SKU data to be mapped.
 * @returns {Object} - The mapped SKU data
 */
const mapCreateSkuData = (detail) => {
  const salePrice = detail?.custom_attributes?.find(
    (data) => data.attribute_code === "special_price"
  );

  const url_key_obj = detail?.custom_attributes?.find(
    (data) => data.attribute_code === "url_key"
  );

  return {
    skuList: [
      {
        skuCode: detail.sku,
        skuName: detail?.name ?? "",
        mrp: detail.price,
        salePrice: salePrice?.value || "",
        taxCategory: "33069000",
        classification: "Normal",
        brand: detail.brand || "",
        vendorCode: "V1",
        baseCost: salePrice?.value || detail.price,
        imageURL: detail.imageURL || "",
        isActive: "yes",
        serialCount: "",
        isSaleable: "yes",
        lottableValidation: "0101",
        pdpURL:
          url_key_obj?.value ??
          `https://www.dentalkart.com/${url_key_obj?.value}.html` ??
          "",
        isBackOrder: "no",
        backOrderQty: "0",
      },
    ],
  };
};

/**
 * Function to make an API call to update SKU.
 * @param {Object} data - The SKU data to be updated.
 * @returns {Object} - API response
 */
const updateSku = async (detail) => {
  const transformedData = mapUpdateSkuData(detail);

  console.log(transformedData);

  const headers = {
    "Content-Type": "application/json",
    ApiKey: API.vinculum.api_key,
    ApiOwner: API.vinculum.api_owner,
  };

  try {
    const response = await axios.post(
      API.vinculum.update_sku_url,
      transformedData,
      { headers }
    );
    return response;
  } catch (error) {
    console.log(
      `+++Vinculum Update failed++++: ${error.message}`,
      JSON.stringify(detail)
    );
  }
};

/**
 * Function to make an API call to create SKU.
 * @param {Object} detail - The SKU data to be created.
 * @returns {Object} - API response
 */
const createSku = async (detail) => {
  const headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  };

  const transformedData = mapCreateSkuData(detail);

  const data = new URLSearchParams({
    RequestBody: JSON.stringify(transformedData),
    ApiKey: API.vinculum.api_key,
    ApiOwner: API.vinculum.api_owner,
  });

  try {
    const response = await axios.post(API.vinculum.create_sku_url, data, {
      headers,
    });
    return response;
  } catch (error) {
    console.log(
      `+++Vinculum Create failed++++: ${error.message}`,
      JSON.stringify(detail)
    );
  }
};

/**
 * Common function to create or update SKU.
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 * @param {Object} context - Lambda context
 * @returns {Object} - API Gateway Lambda Proxy Output Format
 */
export const createOrUpdateSkuHandler = async (event, context) => {
  try {
    console.log("Creating or Updating SKU event", JSON.stringify(event));

    const { detail } = event;

    console.log("++++++vinculuemnSyncHandler++++++", JSON.stringify(detail));

    let response;

    try {
      response = await updateSku(detail);

      console.log(
        "++++++++++Vinculumn_Update_response++++++",
        JSON.stringify(response.data)
      );

      const isSkuNotFound = response.data?.responseStatus?.some(
        (status) => status.responseCode === "306"
      );

      if (isSkuNotFound) {
        console.log(
          "SKU Code not found (responseCode 306), attempting to create SKU..."
        );
        response = await createSku(detail);
        console.log(
          "Create Response after Update Response 306:",
          JSON.stringify(response.data)
        );
      }
    } catch (updateError) {
      console.log("++++createOrUpdateSkuHandler++++.", updateError);
    }

    return {
      statusCode: response?.data?.responseStatus,
      body: JSON.stringify(response.data),
    };
  } catch (e) {
    console.log("Error in create or update SKU function", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
