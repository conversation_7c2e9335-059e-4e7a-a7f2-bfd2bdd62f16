AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Utility Lambda functions to make a cron api request

####################################################################################
Parameters:
####################################################################################

  LambdaRate:
    Description: >
      The rate (frequency) that determines when Cloud<PERSON>atch Events runs the rule that
      triggers the Lambda function.
    Default: rate(1 minute)
    AllowedValues:
      - rate(1 minute)
      - rate(5 minutes)
      - rate(10 minutes)
    Type: String


Resources:
  #**********************************************************************************#
  # Lambda for receiving data
  #**********************************************************************************#

  # Creates lambda for calling utility vin update inventory sync
  VinculumInventorySyncHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: syncHandler.lambdaHandler
      Layers:
        - !Ref AxiosLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  LambdaExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  LambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref LambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${VinculumInventorySyncHandler.Arn}
          Id: LambdaSchedule

  #Create a Permission
  LambdaSchedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !Sub ${VinculumInventorySyncHandler.Arn}
      Principal: 'events.amazonaws.com'
      SourceArn: !Sub ${LambdaSchedule.Arn}


  #**************************************************************************************#
  # Lambda for receiving source-product-data and updating utility's inventory-product-api
  #**************************************************************************************#

  # Creates a lambda function
  ReceiveSourceProductAndUpdateInventoryProduct:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: product.UpdateInventoryProductHandler
      Layers:
        - !Ref AxiosLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  ReceiveSourceProductEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "ReceiveSourceProductEventRule"
      Name: "UtilityProductEventBusRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.catalog.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt ReceiveSourceProductAndUpdateInventoryProduct.Arn
          Id: UpdateInventoryProductHandlerTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: ReceiveSourceProductAndUpdateInventoryProduct
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "ReceiveSourceProductEventRule"
          - "Arn"


  #Create axios layer
  AxiosLayer:
        Type: AWS::Serverless::LayerVersion
        Properties:
            LayerName: axios
            Description: Dependencies for utility
            ContentUri: dependencies/
            CompatibleRuntimes:
              - nodejs6.10
              - nodejs8.10
              - nodejs18.x
            LicenseInfo: 'MIT'
            RetentionPolicy: Retain
