/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

const ConvertToDecimal = (value, decimal_places=2) => {
    const parsedValue = parseFloat(value);
    if (!isNaN(parsedValue)) {
        return parsedValue.toFixed(decimal_places);
    }
    return null;
}

const GetDispatchDays = (dayCode) => {
    const DAY_MAP = {
        "6952": 1,
        "1284": 2,
        "1285": 3,
        "1286": 4,
        "1287": 5,
        "1288": 6,
        "6944": 7,
        "6945": 8,
        "6946": 9,
        "6947": 10,
        "6948": 11,
        "6949": 12,
        "6950": 13,
        "6954": 14,
        "6951": 15,
    };

    return DAY_MAP.hasOwnProperty(dayCode) ? DAY_MAP[dayCode] : null;
};

const GetStatus = (statusCode) => {
    return statusCode === 1 ? 1 : 0;
}

const GetSellingPrice = (mrp, special_price, special_price_from_date, special_price_to_date) => {
    if (special_price_from_date){
        special_price_from_date = new Date(special_price_from_date);
    }
    if (special_price_to_date){
        special_price_to_date = new Date(special_price_to_date);
    }
    if (mrp){
        mrp = ConvertToDecimal(mrp);
    }
    if (special_price){
        special_price = ConvertToDecimal(special_price);
    }

    const currentDate = new Date();

    if (!special_price) {
        return mrp;
    }
    if (special_price >= mrp) {
        return mrp;
    }
    if (special_price_from_date && special_price_to_date) {
        if (special_price_from_date <= currentDate && currentDate <= special_price_to_date) {
            return special_price;
        }
        return mrp
    }
    if (special_price_from_date) {
        if (currentDate >= special_price_from_date) {
            return special_price;
        }
        return mrp
    }
    if (special_price_to_date) {
        if (currentDate <= special_price_to_date) {
            return special_price;
        }
        return mrp
    }

    return special_price;
};


const CreateRequestBodyFromProductData = (product_data) => {
    const { custom_attributes, extension_attributes } = product_data;

    // Extract qty with default value of undefined
    const stock_item = extension_attributes?.stock_item;
    const qty = stock_item?.qty ?? 0;
    const mrp = product_data?.price ?? 0;

    let dispatch_days;
    let is_cod;
    let return_period;
    let special_price;
    let special_price_from_date;
    let special_price_to_date;
    if (custom_attributes){
        custom_attributes.forEach((attribute) => {
            if (attribute.attribute_code === "dispatch_days") {
                dispatch_days = GetDispatchDays(attribute.value);
            }else if (attribute.attribute_code === "is_cod") {
                is_cod = attribute.value;
            }else if (attribute.attribute_code === "return_period") {
                return_period = attribute.value;
            }else if (attribute.attribute_code === "special_price") {
                special_price = attribute.value;
            }else if (attribute.attribute_code === "special_from_date") {
                special_price_from_date = attribute.value;
            }else if (attribute.attribute_code === "special_to_date") {
                special_price_to_date = attribute.value;
            }
        });
    }
    

    return JSON.stringify({
        'product_id': product_data?.id,
        'sku': product_data?.sku,
        'name': product_data?.name,
        'status': GetStatus(product_data?.status),
        'price': GetSellingPrice(mrp, special_price, special_price_from_date, special_price_to_date),
        'qty': qty,
        'dispatch_days': dispatch_days,
        'is_cod': is_cod,
        'weight': product_data?.weight,
        'return_period': return_period
    });
}

export const UpdateInventoryProductHandler = async (event, context) => {
    try {
        const { detail: webhook_data } = event;
        if (webhook_data) {
            let url = API.utility_inventory_product_update_api_url;
            let x_api_key = API.utility_inventory_product_update_api_key;
            let body = CreateRequestBodyFromProductData(webhook_data);
            console.log("__body__:", body)
            const config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: url,
                headers: {
                    'x-api-key': x_api_key,
                    'Content-Type': 'application/json'
                },
                data : body
            };
            const res = await axios(config);
            const result = res.data;
            const response = {
                statusCode: 200,
                body: JSON.stringify(result),
            };
            console.log(url, JSON.stringify(result))
            return response;
        }else{
            return {
                statusCode: 200,
                body: JSON.stringify({
                    isError: false,
                    data: null,
                    message: "Webhook data of undefined",
                }),
            };
        }
    } catch (e) {
        console.log("Error:", e);
        return {
            statusCode: 500,
            body: JSON.stringify({
                isError: true,
                message: e?.message || e,
                error: e?.message || e,
            }),
        };
    }
};
