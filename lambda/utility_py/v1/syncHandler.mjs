/**
 *
 * Event doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html#api-gateway-simple-proxy-for-lambda-input-format
 * @param {Object} event - API Gateway Lambda Proxy Input Format
 *
 * Context doc: https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-model-context.html
 * @param {Object} context
 *
 * Return doc: https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html
 * @returns {Object} object - API Gateway Lambda Proxy Output Format
 *
 */
import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
    try {
        let url = API.vin_inventory_sync_api_url;
        let x_api_key = API.vin_inventory_sync_api_key;
        const config = {
            method: 'post',
            url: url,
            headers: {
                'x-api-key': x_api_key
            }
        };

        const res = await axios(config);
        const result = res.data;

        const response = {
            statusCode: 200,
            body: JSON.stringify(result),
        };
        console.log(url, JSON.stringify(result))
        return response;
    } catch (e) {
        return {
            statusCode: 500,
            body: JSON.stringify({
                message: e?.message || e,
                error: e?.message || e,
            }),
        };
    }
};