AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Algolia Lambda functions to receive and sync data

####################################################################################
Parameters:
  ####################################################################################

  LambdaRate:
    Description: >
      The rate (frequency) that determines when CloudWatch Events runs the rule that
      triggers the Lambda function.
    Default: rate(1 minutes)
    AllowedValues:
      - rate(1 minute)
      - rate(5 minutes)
      - rate(10 minutes)
    Type: String

  SyncProductLambdaRate:
    Description: >
      The rate (frequency) that determines when CloudWatch Events runs the rule that
      triggers the Lambda function.
    Default: "rate(2 minutes)"
    AllowedValues:
      - "rate(2 minutes)"
      - "rate(5 minutes)"
      - "rate(10 minutes)"
    Type: String

Resources:
  #**********************************************************************************#
  # Lambda for receiving data
  #**********************************************************************************#

  # Creates lambda for receiving product data
  # AlgoliaGetDataFunction:
  #   Type: AWS::Serverless::Function
  #   Properties:
  #     CodeUri: ./v1
  #     Handler: app.lambdaHandler
  #     Layers:
  #       - !Ref AxiosLayer
  #     Runtime: nodejs18.x
  #     MemorySize: 128
  #     Timeout: 15
  #     Architectures:
  #       - x86_64

  #Create a Role
  # AlgoliaEventRule:
  #   Type: AWS::Events::Rule
  #   Properties:
  #     Description: "AlgoliaEventRule"
  #     Name: "AlgoliaEventBusRule001"
  #     EventBusName: dentalkart-eventbus-prod
  #     EventPattern:
  #       source:
  #         - dentalkart.catalog.service

  #     State: ENABLED
  #     Targets:
  #       - Arn: !GetAtt AlgoliaGetDataFunction.Arn
  #         Id: AlgoliaLambdaTarget
  #         RetryPolicy:
  #           MaximumRetryAttempts: 4
  #           MaximumEventAgeInSeconds: 400

  #Create a Permission
  # PermissionForEventsToInvokeLambda:
  #   Type: AWS::Lambda::Permission
  #   Properties:
  #     FunctionName:
  #       Ref: AlgoliaGetDataFunction
  #     Action: "lambda:InvokeFunction"
  #     Principal: "events.amazonaws.com"
  #     SourceArn:
  #       Fn::GetAtt:
  #         - "AlgoliaEventRule"
  #         - "Arn"

  #**********************************************************************************#
  # Lambda for syncing data in algolia
  #**********************************************************************************#

  # Creates lambda for syncing products in algolia
  AlgoliaSyncHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: syncHandler.lambdaHandler
      Layers:
        - !Ref AxiosLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  LambdaExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  LambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref LambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${AlgoliaSyncHandlerFunction.Arn}
          Id: LambdaSchedule

  #Create a Permission
  LambdaSchedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !Sub ${AlgoliaSyncHandlerFunction.Arn}
      Principal: "events.amazonaws.com"
      SourceArn: !Sub ${LambdaSchedule.Arn}

  # Creates lambda for syncing products in algolia
  SynDisabledProductsHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: syncDisabledProducts.lambdaHandler
      Layers:
        - !Ref AxiosLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 20
      Architectures:
        - x86_64

  #Create a Role
  SynDisabledProductsExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  SynDisabledProductsLambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref SyncProductLambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${SynDisabledProductsHandler.Arn}
          Id: SynDisabledProducts

  #Create a Permission
  SynEnabledProductsLambdaSchedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !Sub ${SynDisabledProductsHandler.Arn}
      Principal: "events.amazonaws.com"
      SourceArn: !Sub ${SynDisabledProductsLambdaSchedule.Arn}

  # Creates lambda for syncing products in algolia
  SynEnabledProductsHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: syncEnabledProducts.lambdaHandler
      Layers:
        - !Ref AxiosLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 25
      Architectures:
        - x86_64

  #Create a Role
  SynEnabledProductsExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  #Create a Scheduler
  SynEnabledProductsLambdaSchedule:
    Type: "AWS::Events::Rule"
    Properties:
      Description: >
        A schedule for the Lambda function..
      ScheduleExpression: !Ref SyncProductLambdaRate
      State: ENABLED
      Targets:
        - Arn: !Sub ${SynEnabledProductsHandler.Arn}
          Id: SynEnabledProducts

  #Create a Permission
  SynEnabledProductschedulePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      Action: "lambda:InvokeFunction"
      FunctionName: !Sub ${SynEnabledProductsHandler.Arn}
      Principal: "events.amazonaws.com"
      SourceArn: !Sub ${SynEnabledProductsLambdaSchedule.Arn}

  #Create axios layer
  AxiosLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: axios
      Description: Dependencies for algolia
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
