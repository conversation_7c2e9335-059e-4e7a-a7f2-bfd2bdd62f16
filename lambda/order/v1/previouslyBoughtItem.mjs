import axios from "axios";
import { API } from "./config/api.mjs";

export const lambdaHandler = async (event, context) => {
  try {
    const { detail: webook_data } = event;
    if (webook_data) {
      const res = await axios.post(API.previously_bought_item_url, webook_data, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      return {
        statusCode: 200,
        body: JSON.stringify({
          data: res?.data,
        }),
      };
    } else {
      console.log("Webhook data of undefined", event?.detail);
      return {
        statusCode: 200,
        body: JSON.stringify({
          isError: false,
          data: null,
          message: "Webhook data of undefined",
        }),
      };
    }
  } catch (e) {
    console.log("error_in_previously_bought_item_webhook", event?.detail);
    console.log("error_in_vinc_previously_bought_item_webhook", e);
    return {
      statusCode: 500,
      body: JSON.stringify({
        isError: true,
        data: null,
        message: e?.message,
      }),
    };
  }
};
