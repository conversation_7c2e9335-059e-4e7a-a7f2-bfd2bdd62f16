AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Order Lambda functions to sync data

Resources:
  # Creates lambda for syncing order data
  OrderFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref OrderDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  # Creates lambda for syncing order data in vinc
  SyncOrderVincFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: sync_order_vinc.lambdaHandler
      Layers:
        - !Ref OrderDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  # Creates lambda for cancel order in order service
  CancelOrderFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: cancelOrder.lambdaHandler
      Layers:
        - !Ref OrderDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  # Creates lambda for previously bought item in order service
  PreviouslyBoughtItemFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: previouslyBoughtItem.lambdaHandler
      Layers:
        - !Ref OrderDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  OrderEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "OrderEventRule"
      Name: "OrderEventRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.order.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt OrderFunction.Arn
          Id: AlgoliaLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400
        - Arn: arn:aws:sqs:ap-south-1:834602855693:OrderTrackingQueue
          Id: OrderTrackingQueueTarget

  #Create a Role SyncOrderVincFunction
  SyncOrderVincEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "SyncOrderVincFunctionEventRule"
      Name: "SyncOrderVincFunctionEventRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.order.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt SyncOrderVincFunction.Arn
          Id: SyncOrderVincFunctionTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Role
  CancelOrderEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "CancelOrderEventRule"
      Name: "CancelOrderEventRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.cancelOrder.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt CancelOrderFunction.Arn
          Id: CancelOrderLmdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Role
  PreviouslyBoughtItemEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "PreviouslyBoughtItemEventRule"
      Name: "PreviouslyBoughtItemEventRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.order.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt PreviouslyBoughtItemFunction.Arn
          Id: PreviouslyBoughtItemLambdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: OrderFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "OrderEventRule"
          - "Arn"

  #Create a Permission
  PermissionForEventsToInvokeSyncOrderVincLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: SyncOrderVincFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "SyncOrderVincEventRule"
          - "Arn"

  #Create a Permission
  PermissionForEventsToInvokeCancelOrderLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: CancelOrderFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "CancelOrderEventRule"
          - "Arn"

  PermissionForEventsToInvokePreviouslyBoughtItemLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: PreviouslyBoughtItemFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "PreviouslyBoughtItemEventRule"
          - "Arn"

  #Create axios layer
  OrderDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: orderDependencies
      Description: Dependencies for order
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
