AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: AdminTierPrice lambda function for sending chunks

Resources:
  # creating the lambda
  AdminTierPriceRequestHandlerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./v1
      Handler: app.lambdaHandler
      Layers:
        - !Ref AdminTierPriceDependenciesLayer
      Runtime: nodejs18.x
      MemorySize: 128
      Timeout: 15
      Architectures:
        - x86_64

  #Create a Role
  AdminTierPriceEventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Event rule for calling AdminTierPriceRequestHandlerFunction"
      Name: "AdminTierPriceEventRule"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.admintierprice.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt AdminTierPriceRequestHandlerFunction.Arn
          Id: PreRenderLamdaTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 400

  #Create a Permission
  PermissionForEventsToInvokeAdminTierPriceLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: AdminTierPriceRequestHandlerFunction
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "AdminTierPriceEventRule"
          - "Arn"

  #Create axios layer
  AdminTierPriceDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: AdminTierPriceDependencies
      Description: Dependencies for Admin Tier Price
      ContentUri: dependencies/
      CompatibleRuntimes:
        - nodejs6.10
        - nodejs8.10
        - nodejs18.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
