AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Create Bus

Resources:
  # Creates event Bus
  DkartEventBus:
    Type: AWS::Events::EventBus
    Properties:
      Name: dentalkart-eventbus-prod

  # Creates role and LogGroup for Event Bus
  EventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "EventRule"
      Name: "EventBusRule001"
      EventBusName: dentalkart-eventbus-prod
      EventPattern:
        source:
          - dentalkart.catalog.service

      State: ENABLED
      Targets:
        - Arn: !GetAtt LogGroupForEvents.Arn
          Id: LogTarget
          RetryPolicy:
            MaximumRetryAttempts: 4
            MaximumEventAgeInSeconds: 86400

  LogGroupForEvents:
    Type: AWS::Logs::LogGroup
    Properties:
      # LogGroupName: LogGroupForEvents
      LogGroupName: logGroup-for-events-prod
      RetentionInDays: 3
  LogGroupForEventsPolicy:
    Type: AWS::Logs::ResourcePolicy
    Properties:
      PolicyName: EventBridgeToCWLogsPolicy
      PolicyDocument: !Sub >
        {
          "Version": "2012-10-17",
          "Statement": [
            {
              "Sid": "EventBridgetoCWLogsCreateLogStreamPolicy",
              "Effect": "Allow",
              "Principal": {
                "Service": [
                  "events.amazonaws.com"
                ]
              },
              "Action": [
                "logs:CreateLogStream",
                "logs:PutLogEvents"
              ],
              "Resource": [
                "${LogGroupForEvents.Arn}"
              ]
            }
          ]
        }
